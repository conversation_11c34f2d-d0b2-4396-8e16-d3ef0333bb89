# README.md

This project uses Ansible Vault to securely store sensitive data like API keys, tokens, and passwords.

## Initial Setup

1. **Create the vault file from template:**
   ```bash
   cp vault.yml.template vault.yml
   ```

2. **Encrypt the vault file:**
   ```bash
   ansible-vault encrypt vault.yml
   ```
   You'll be prompted to create a vault password. **Remember this password!**

3. **Delete the template file (optional but recommended):**
   ```bash
   rm vault.yml.template
   ```

## Running Playbooks

When running your playbook, you'll need to provide the vault password:

### Option 1: Prompt for password
```bash
ansible-playbook -i inventory.yml playbook.yml --ask-vault-pass
```

### Option 2: Use a password file
```bash
echo "your_vault_password" > .vault_pass
chmod 600 .vault_pass
ansible-playbook -i inventory.yml playbook.yml --vault-password-file .vault_pass
```

**Important:** Add `.vault_pass` to your `.gitignore` if you use this method.

### Option 3: Use environment variable
```bash
export ANSIBLE_VAULT_PASSWORD_FILE=.vault_pass
ansible-playbook -i inventory.yml playbook.yml
```

## Managing Vault Content

### View encrypted vault content:
```bash
ansible-vault view vault.yml
```

### Edit encrypted vault content:
```bash
ansible-vault edit vault.yml
```

### Change vault password:
```bash
ansible-vault rekey vault.yml
```

### Decrypt vault (temporarily):
```bash
ansible-vault decrypt vault.yml
# Make changes
ansible-vault encrypt vault.yml
```

## Security Best Practices

1. **Never commit unencrypted vault files** - The `.gitignore` file is configured to exclude `vault.yml`
2. **Use strong vault passwords** - Consider using a password manager
3. **Rotate secrets regularly** - Update API keys and tokens periodically
4. **Limit access** - Only share vault passwords with authorized team members
5. **Backup vault passwords** - Store them securely outside of the repository

## Vault Variables

The following sensitive variables are stored in the vault:

- `vault_cookie_signing_key` - Cookie signing key for ZYR application
- `vault_deepseek_api_key` - DeepSeek API key
- `vault_postgres_connection_string` - PostgreSQL connection string with credentials
- `vault_tmdb_api_key` - The Movie Database API key
- `vault_wikimedia_access_token` - Wikimedia API access token

## Troubleshooting

### "Vault password incorrect" error
- Ensure you're using the correct vault password
- Check if the vault file is properly encrypted with `ansible-vault view vault.yml`

### "Variable not found" error
- Verify that all vault variables are defined in `vault.yml`
- Check that `vars_files: - vault.yml` is included in your playbook

### Permission denied errors
- Ensure vault files have appropriate permissions (600 or 644)
- Check that your user has read access to the vault file
