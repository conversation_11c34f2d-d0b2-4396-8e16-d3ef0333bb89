---
- name: create keyrings directory
  ansible.builtin.file:
    path: /etc/apt/keyrings
    state: directory
  become: true
- name: download syncthing-archive-keyring.gpg
  ansible.builtin.get_url:
    url: https://syncthing.net/release-key.gpg
    dest: /etc/apt/keyrings/syncthing-archive-keyring.gpg
  become: true
- name: copy syncthing.list
  ansible.builtin.template:
    src: syncthing.list
    dest: "/etc/apt/sources.list.d/syncthing.list"
  become: true
- name: install syncthing
  ansible.builtin.apt:
    name: syncthing
    update_cache: yes
    state: present
  become: true
