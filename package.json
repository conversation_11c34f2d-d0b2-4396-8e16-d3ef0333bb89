{"name": "popcorn", "version": "1.0.0", "description": "", "private": true, "type": "module", "scripts": {"build:kinopoisk": "esbuild ./bin/kinopoisk.ts --bundle --platform=node --external:pg-native --outfile=./dist/kinopoisk.js", "build:llm": "esbuild ./bin/llm.ts --bundle --platform=node --external:pg-native --outfile=./dist/llm.js", "build:tmdb": "esbuild ./bin/tmdb.ts --bundle --platform=node --external:pg-native --outfile=./dist/tmdb.js", "build:web": "NODE_ENV=production vite build --mode=production --config=vite.config.ts ./src/web && NODE_ENV=production esbuild ./bin/zyr.ts --bundle --platform=node --external:pg-native --keep-names --outfile=./dist/zyr.js", "build:wikidata": "esbuild ./bin/wikidata.ts --bundle --platform=node --external:pg-native --outfile=./dist/wikidata.js", "build:wikimedia": "esbuild ./bin/wikimedia.ts --bundle --platform=node --external:pg-native --outfile=./dist/wikimedia.js", "generate": "graphql-codegen-esm --config codegen.yml", "lint": "tsc --noEmit && eslint ** --ext .ts,.tsx,.js --no-error-on-unmatched-pattern", "migrate": "dotenvx run -- node --loader ts-node/esm ./scripts/migrate.ts", "prepare": "husky install", "start:kinanet": "dotenvx run -- node --loader ts-node/esm ./bin/kinanet.ts", "start:kinopoisk": "dotenvx run -- node --loader ts-node/esm --inspect ./bin/kinopoisk.ts", "start:llm": "dotenvx run -- node --loader ts-node/esm ./bin/llm.ts", "start:tmdb": "dotenvx run -- node --loader ts-node/esm ./bin/tmdb.ts", "start:web": "concurrently --raw 'dotenvx run -- node --loader ts-node/esm ./bin/zyr.dev.ts' 'graphql-codegen-esm --config codegen.yml --watch'", "start:wikidata": "dotenvx run -- node --loader ts-node/esm ./bin/wikidata.ts", "start:wikimedia": "dotenvx run -- node --loader ts-node/esm ./bin/wikimedia.ts"}, "engines": {"node": "=20.19.4", "npm": "=10.8.2"}, "author": "", "license": "ISC", "devDependencies": {"@commitlint/cli": "^17.7.1", "@commitlint/config-conventional": "^17.7.0", "@dotenvx/dotenvx": "^1.38.3", "@graphql-codegen/add": "^5.0.3", "@graphql-codegen/cli": "^5.0.5", "@graphql-codegen/client-preset": "^4.6.4", "@graphql-codegen/typescript": "^4.1.5", "@graphql-codegen/typescript-operations": "^4.5.1", "@graphql-codegen/typescript-resolvers": "^4.4.4", "@parcel/watcher": "^2.3.0", "@simplewebauthn/typescript-types": "^7.4.0", "@swc/core": "^1.3.75", "@tailwindcss/postcss": "^4.0.8", "@types/cookies": "0.7.7", "@types/docopt": "^0.6.33", "@types/lodash.isequal": "^4.5.6", "@types/lodash.shuffle": "^4.2.8", "@types/md5": "^2.3.1", "@types/pdf-parse": "^1.1.4", "@types/pg": "^8.6.1", "@types/pg-copy-streams": "^1.2.1", "@types/react-dom": "^18.2.4", "@typescript-eslint/eslint-plugin": "^6.4.0", "@typescript-eslint/parser": "^6.4.0", "concurrently": "^7.2.1", "dotenv": "^10.0.0", "esbuild": "^0.13.12", "eslint": "^8.57.1", "eslint-kit": "^11.26.0", "husky": "^8.0.3", "lint-staged": "^14.0.1", "postcss": "^8.3.11", "prettier": "^3.0.2", "prettier-plugin-tailwindcss": "^0.6.11", "react-dom": "^18.2.0", "tailwindcss": "^4.0.0", "ts-node": "^10.4.0", "typescript": "^4.4.4"}, "dependencies": {"@graphql-tools/schema": "^10.0.0", "@graphql-typed-document-node/core": "^3.2.0", "@simplewebauthn/browser": "^7.4.0", "@simplewebauthn/server": "^7.4.0", "@tanstack/react-query": "^4.33.0", "@types/graphql-fields": "^1.3.9", "@vitejs/plugin-react": "^4.3.4", "cacheable-lookup": "5.0.4", "classnames": "^2.3.1", "cookies": "0.8.0", "css-select": "^4.1.3", "csv-stringify": "^6.5.2", "date-fns": "^2.25.0", "docopt": "^0.6.2", "domhandler": "^4.3.0", "domutils": "^2.8.0", "got": "^11.8.2", "graphql": "^16.3.0", "graphql-fields": "^2.0.3", "graphql-tag": "2.12.6", "graphql-yoga": "5.12.0", "hpagent": "^1.2.0", "htmlparser2": "^7.2.0", "isbot": "^3.6.13", "lodash.isequal": "^4.5.0", "lodash.shuffle": "^4.2.0", "magic-bytes.js": "^1.5.0", "md5": "^2.3.0", "minisearch": "^6.3.0", "ollama": "^0.5.11", "openai": "^4.87.3", "pdf-parse": "^1.1.1", "pg": "^8.7.1", "pg-copy-streams": "^6.0.2", "postgrator": "^5.0.0", "puppeteer": "^20.1.2", "react": "^18.2.0", "react-dom": "^18.2.0", "sql-template-strings": "^2.2.2", "ts-node": "^10.4.0", "url-pattern": "^1.0.3", "vite": "^6.2.0"}}