import assert from "assert";
import got from "got";
import shuffle from "lodash.shuffle";

import {
  WikidataGateway,
  WikidataGatewayFindGenresByWikidataIdsItem,
  WikidataGatewayFindGenresByWikidataIdsResponse,
  WikidataGatewayFindMoviesByKinopoiskIdsItem,
  WikidataGatewayFindMoviesByKinopoiskIdsResponse,
  WikidataGatewayFindMoviesByWikidataIdsItem,
  WikidataGatewayFindMoviesByWikidataIdsResponse,
  WikidataGatewayFindPeopleByWikidataIdsItem,
  WikidataGatewayFindPeopleByWikidataIdsResponse,
} from "../jobs/PullWikidataMoviesJob.js";

export default class WikidataSparqlGateway implements WikidataGateway {
  // eslint-disable-next-line class-methods-use-this
  async findMoviesByKinopoiskIds(
    kinopoiskIds: string[],
  ): Promise<WikidataGatewayFindMoviesByKinopoiskIdsResponse> {
    const result: WikidataGatewayFindMoviesByKinopoiskIdsResponse = [];

    for (const chunk of chunks(kinopoiskIds, 1000)) {
      const response = await sparql<FindMoviesByKinopoiskIdsResponse>(`
        SELECT ?movie ?kinopoiskId
        WHERE
        {
          ?movie wdt:P2603 ?kinopoiskId.
          VALUES ?kinopoiskId {
            ${chunk.map((id) => `"${id}"`).join("\n")}
          }
        }
      `);

      result.push(
        ...response.results.bindings.map(
          (binding): WikidataGatewayFindMoviesByKinopoiskIdsItem => {
            const id = /http:\/\/www.wikidata.org\/entity\/Q(\d+)/.exec(
              binding.movie.value,
            )?.[1];

            assert(id, "Expected 'movie' binding to match pattern");

            return {
              id,
              kinopoiskId: binding.kinopoiskId.value,
            };
          },
        ),
      );
    }

    return result;
  }

  // eslint-disable-next-line class-methods-use-this
  async findMoviesByWikidataIds(
    wikidataIds: string[],
  ): Promise<WikidataGatewayFindMoviesByWikidataIdsResponse> {
    const result: (WikidataGatewayFindMoviesByWikidataIdsItem | null)[] = [];

    for (const chunk of chunks(wikidataIds, 1000)) {
      const separator = ";";
      const response = await sparql<FetchMoviesResponse>(`
        SELECT
          ?movie
          (SAMPLE(?_titleEn) as ?titleEn)
          (SAMPLE(?_titleRu) as ?titleRu)
          (SAMPLE(?_wikipediaEnUrl) as ?wikipediaEnUrl)
          (SAMPLE(?_wikipediaRuUrl) as ?wikipediaRuUrl)
          (SAMPLE(?_kinopoiskId) as ?kinopoiskId)
          (SAMPLE(?_imdbId) as ?imdbId)
          (SAMPLE(?_tmdbId) as ?tmdbId)
          (MAX(?_duration) as ?duration)
          (GROUP_CONCAT(?director; separator="${separator}") as ?directors)
          (GROUP_CONCAT(?genre; separator="${separator}") as ?genres)
        WHERE 
        {
          VALUES ?movie {
            ${
              // Indident on 2025-06-06 shown that shuffling helps against cached empty rows
              shuffle(chunk)
                .map((id) => `wd:Q${id}`)
                .join("\n")
            }
          }
          OPTIONAL {
            ?movie wdt:P2603 ?_kinopoiskId
          }
          OPTIONAL {
            ?movie wdt:P57 ?director
          }
          OPTIONAL {
            ?movie wdt:P136 ?genre
          }
          OPTIONAL {
            ?movie wdt:P345 ?_imdbId
          }
          OPTIONAL {
            ?movie wdt:P4947 ?_tmdbId
          }
          OPTIONAL {
            ?movie wdt:P2047 ?_duration
          }
          OPTIONAL {
            SERVICE wikibase:label {
              bd:serviceParam wikibase:language "en" .
              ?movie rdfs:label ?_titleEn .
            }
          }
          OPTIONAL {
            SERVICE wikibase:label {
              bd:serviceParam wikibase:language "ru" .
              ?movie rdfs:label ?_titleRu .
            }
          }
          OPTIONAL {
            ?_wikipediaEnUrl schema:about ?movie .
            ?_wikipediaEnUrl schema:inLanguage "en" .
            ?_wikipediaEnUrl schema:isPartOf <https://en.wikipedia.org/> .
          }
          OPTIONAL {
            ?_wikipediaRuUrl schema:about ?movie .
            ?_wikipediaRuUrl schema:inLanguage "ru" .
            ?_wikipediaRuUrl schema:isPartOf <https://ru.wikipedia.org/> .
          }
        }
        GROUP BY ?movie
      `);

      const results = response.results.bindings
        .map((binding): WikidataGatewayFindMoviesByWikidataIdsItem | null => {
          const directors =
            binding.directors.value === ""
              ? []
              : binding.directors.value.split(separator);
          const genres =
            binding.genres.value === ""
              ? []
              : binding.genres.value.split(separator);
          const id = /http:\/\/www.wikidata.org\/entity\/Q(\d+)/.exec(
            binding.movie.value,
          )?.[1];

          if (!id) {
            return null;
          }

          return {
            id,
            title: {
              en: binding.titleEn?.["xml:lang"]
                ? normalizeMovieTitle(binding.titleEn.value, "en")
                : undefined,
              ru: binding.titleRu?.["xml:lang"]
                ? normalizeMovieTitle(binding.titleRu.value, "ru")
                : undefined,
            },
            imdbId: binding.imdbId?.value,
            kinopoiskId: binding.kinopoiskId?.value,
            directors: uniqBy(
              (director) => director.id,
              directors
                .map((director) => ({
                  id: /http:\/\/www.wikidata.org\/entity\/Q(\d+)/.exec(
                    director,
                  )?.[1],
                }))
                .filter((x): x is { id: string } => x.id !== undefined),
            ),
            genres: uniqBy(
              (genre) => genre.id,
              genres
                .map((genre) => ({
                  id: /http:\/\/www.wikidata.org\/entity\/Q(\d+)/.exec(
                    genre,
                  )?.[1],
                }))
                .filter((x): x is { id: string } => x.id !== undefined),
            ),
            duration: binding.duration
              ? {
                  minutes: Math.round(Number(binding.duration.value)),
                }
              : undefined,
            tmdbId: binding.tmdbId?.value,
            wikipediaSlugs: {
              en: binding.wikipediaEnUrl
                ? decodeURIComponent(
                    binding.wikipediaEnUrl.value.replace(
                      "https://en.wikipedia.org/wiki/",
                      "",
                    ),
                  )
                : undefined,
              ru: binding.wikipediaRuUrl
                ? decodeURIComponent(
                    binding.wikipediaRuUrl.value.replace(
                      "https://ru.wikipedia.org/wiki/",
                      "",
                    ),
                  )
                : undefined,
            },
          };
        })
        .filter(
          (x): x is WikidataGatewayFindMoviesByWikidataIdsItem =>
            x !== null &&
            !(
              x.directors.length === 0 &&
              x.duration == null &&
              x.imdbId == null &&
              x.kinopoiskId == null &&
              x.title.en == null &&
              x.title.ru == null &&
              x.tmdbId == null &&
              x.wikipediaSlugs.en == null &&
              x.wikipediaSlugs.ru == null
            ),
        );
      const hash = new Map(results.map((m) => [m.id, m]));

      result.push(...chunk.map((id) => hash.get(id) ?? null));
    }

    return result;
  }

  // eslint-disable-next-line class-methods-use-this
  async findGenresByWikidataIds(
    wikidataIds: string[],
  ): Promise<WikidataGatewayFindGenresByWikidataIdsResponse> {
    const result: (WikidataGatewayFindGenresByWikidataIdsItem | null)[] = [];

    for (const chunk of chunks(wikidataIds, 1000)) {
      const response = await sparql<FetchGenresResponse>(`
        SELECT ?genre ?labelEn ?labelRu ?wikipediaEnUrl ?wikipediaRuUrl
        WHERE
        {
          VALUES ?genre {
            ${
              // Indident on 2025-06-06 shown that shuffling helps against cached empty rows
              shuffle(chunk)
                .map((id) => `wd:Q${id}`)
                .join("\n")
            }
          }
          OPTIONAL {
            SERVICE wikibase:label {
              bd:serviceParam wikibase:language "en".
              ?genre rdfs:label ?labelEn .
            }
          }
          OPTIONAL {
            SERVICE wikibase:label {
              bd:serviceParam wikibase:language "ru".
              ?genre rdfs:label ?labelRu .
            }
          }
          OPTIONAL {
            ?wikipediaEnUrl schema:about ?genre .
            ?wikipediaEnUrl schema:inLanguage "en" .
            ?wikipediaEnUrl schema:isPartOf <https://en.wikipedia.org/> .
          }
          OPTIONAL {
            ?wikipediaRuUrl schema:about ?genre .
            ?wikipediaRuUrl schema:inLanguage "ru" .
            ?wikipediaRuUrl schema:isPartOf <https://ru.wikipedia.org/> .
          }
        }
      `);

      const results = response.results.bindings
        .map((binding): WikidataGatewayFindGenresByWikidataIdsItem | null => {
          const id = /http:\/\/www.wikidata.org\/entity\/Q(\d+)/.exec(
            binding.genre.value,
          )?.[1];

          if (!id) {
            return null;
          }

          return {
            id,
            label: {
              en: binding.labelEn["xml:lang"]
                ? binding.labelEn.value
                : undefined,
              ru: binding.labelRu["xml:lang"]
                ? binding.labelRu.value
                : undefined,
            },
            wikipediaSlugs: {
              en: binding.wikipediaEnUrl
                ? decodeURIComponent(
                    binding.wikipediaEnUrl.value.replace(
                      "https://en.wikipedia.org/wiki/",
                      "",
                    ),
                  )
                : undefined,
              ru: binding.wikipediaRuUrl
                ? decodeURIComponent(
                    binding.wikipediaRuUrl.value.replace(
                      "https://ru.wikipedia.org/wiki/",
                      "",
                    ),
                  )
                : undefined,
            },
          };
        })
        .filter(
          (x): x is WikidataGatewayFindGenresByWikidataIdsItem =>
            x !== null &&
            !(
              x.label.en == null &&
              x.label.ru == null &&
              x.wikipediaSlugs.en == null &&
              x.wikipediaSlugs.ru == null
            ),
        );
      const hash = new Map(results.map((m) => [m.id, m]));

      result.push(...chunk.map((id) => hash.get(id) ?? null));
    }

    return result;
  }

  // eslint-disable-next-line class-methods-use-this
  async findPeopleByWikidataIds(
    wikidataIds: string[],
  ): Promise<WikidataGatewayFindPeopleByWikidataIdsResponse> {
    const result: (WikidataGatewayFindPeopleByWikidataIdsItem | null)[] = [];

    for (const chunk of chunks(wikidataIds, 1000)) {
      const response = await sparql<FetchPeopleResponse>(`
        SELECT ?person ?fullNameEn ?fullNameRu ?tmdbId ?wikipediaEnUrl ?wikipediaRuUrl
        WHERE 
        {
          VALUES ?person {
            ${
              // Indident on 2025-06-06 shown that shuffling helps against cached empty rows
              shuffle(chunk)
                .map((id) => `wd:Q${id}`)
                .join("\n")
            }
          }
          OPTIONAL {
            ?person wdt:P4985 ?tmdbId
          }
          OPTIONAL {
            SERVICE wikibase:label {
              bd:serviceParam wikibase:language "en".
              ?person rdfs:label ?fullNameEn .
            }
          }
          OPTIONAL {
            SERVICE wikibase:label {
              bd:serviceParam wikibase:language "ru".
              ?person rdfs:label ?fullNameRu .
            }
          }
          OPTIONAL {
            ?wikipediaEnUrl schema:about ?person .
            ?wikipediaEnUrl schema:inLanguage "en" .
            ?wikipediaEnUrl schema:isPartOf <https://en.wikipedia.org/> .
          }
          OPTIONAL {
            ?wikipediaRuUrl schema:about ?person .
            ?wikipediaRuUrl schema:inLanguage "ru" .
            ?wikipediaRuUrl schema:isPartOf <https://ru.wikipedia.org/> .
          }
        }
      `);

      const results = response.results.bindings
        .map((binding): WikidataGatewayFindPeopleByWikidataIdsItem | null => {
          const id = /http:\/\/www.wikidata.org\/entity\/Q(\d+)/.exec(
            binding.person.value,
          )?.[1];

          if (!id) {
            return null;
          }

          return {
            id,
            fullName: {
              en: binding.fullNameEn["xml:lang"]
                ? normalizeFullName(binding.fullNameEn.value, "en")
                : undefined,
              ru: binding.fullNameRu["xml:lang"]
                ? normalizeFullName(binding.fullNameRu.value, "ru")
                : undefined,
            },
            tmdbId: binding.tmdbId?.value,
            wikipediaSlugs: {
              en: binding.wikipediaEnUrl
                ? decodeURIComponent(
                    binding.wikipediaEnUrl.value.replace(
                      "https://en.wikipedia.org/wiki/",
                      "",
                    ),
                  )
                : undefined,
              ru: binding.wikipediaRuUrl
                ? decodeURIComponent(
                    binding.wikipediaRuUrl.value.replace(
                      "https://ru.wikipedia.org/wiki/",
                      "",
                    ),
                  )
                : undefined,
            },
          };
        })

        .filter(
          (x): x is WikidataGatewayFindPeopleByWikidataIdsItem =>
            x !== null &&
            !(
              x.fullName.en == null &&
              x.fullName.ru == null &&
              x.tmdbId == null &&
              x.wikipediaSlugs.en == null &&
              x.wikipediaSlugs.ru == null
            ),
        );
      const hash = new Map(results.map((m) => [m.id, m]));

      result.push(...chunk.map((id) => hash.get(id) ?? null));
    }

    return result;
  }
}

async function sparql<T>(query: string): Promise<T> {
  const response = await got<T>("https://query.wikidata.org/sparql", {
    body: new URLSearchParams({
      format: "json",
      query,
    }).toString(),
    headers: {
      "content-type": "application/x-www-form-urlencoded",
    },
    method: "POST",
    retry: {
      limit: 5,
    },
    responseType: "json",
  });

  return response.body;
}

function chunks<T>(list: T[], size: number): T[][] {
  const cnks: T[][] = [];
  let chunk: T[] = [];

  list.forEach((item, index) => {
    chunk.push(item);

    if (chunk.length === size || index === list.length - 1) {
      cnks.push(chunk);
      chunk = [];
    }
  });

  return cnks;
}

function normalizeMovieTitle(
  initialTitle: string,
  locale: "en" | "ru",
): string {
  if (locale === "en") {
    return initialTitle.replace(
      / \((film|cartoon|multfilm|\d{4}|\d{4} film|\d{4} movie|\d{4} .* film)\)$/i,
      "",
    );
  }

  return initialTitle.replace(
    / \((мультфильм|фильм|спектакль|короткометражный фильм|мини-сериал|мюзикл|аниме|\d{4}).*\)$/i,
    "",
  );
}

function normalizeFullName(
  initialFullName: string,
  locale: "en" | "ru",
): string {
  let fullName = initialFullName;

  // "Попов, Владимир Иванович (мультипликатор)" -> "Попов, Владимир Иванович"
  fullName = fullName
    .replace(/(\(.*\)?)/, "")
    .replace(/\s{2,}/, " ")
    .trim();

  if (fullName.includes(",")) {
    // "Попов, Владимир Иванович" -> "Владимир Иванович Попов"
    fullName = fullName.split(",").reverse().join(" ").trim();
  }

  if (locale === "ru") {
    const [givenName, middleName, familyName] = fullName.trim().split(" ");

    if (
      middleName &&
      familyName &&
      (middleName.endsWith("ич") || middleName.endsWith("на"))
    ) {
      return [givenName, familyName].join(" ");
    }
  }

  return fullName;
}

function uniqBy<T>(fn: (t: T) => string, items: T[]): T[] {
  const hashes = new Set<string>();
  const result: T[] = [];

  items.forEach((item) => {
    const hash = fn(item);

    if (!hashes.has(hash)) {
      result.push(item);
      hashes.add(hash);
    }
  });

  return result;
}

interface FindMoviesByKinopoiskIdsResponse {
  results: {
    bindings: {
      movie: {
        type: "uri";
        value: string;
      };
      kinopoiskId: {
        type: "literal";
        value: string;
      };
    }[];
  };
}

interface FetchMoviesResponse {
  results: {
    bindings: {
      movie: {
        type: "uri";
        value: string;
      };
      titleEn?: {
        type: "literal";
        value: string;
        "xml:lang"?: "en";
      };
      titleRu?: {
        type: "literal";
        value: string;
        "xml:lang"?: "ru";
      };
      imdbId?: {
        type: "literal";
        value: string;
      };
      kinopoiskId?: {
        type: "literal";
        value: string;
      };
      directors: {
        type: "literal";
        value: string;
      };
      genres: {
        type: "literal";
        value: string;
      };
      duration?: {
        datatype: "http://www.w3.org/2001/XMLSchema#decimal";
        type: "literal";
        value: string;
      };
      tmdbId?: {
        type: "literal";
        value: string;
      };
      wikipediaEnUrl?: {
        type: "uri";
        value: string;
      };
      wikipediaRuUrl?: {
        type: "uri";
        value: string;
      };
    }[];
  };
}

interface FetchGenresResponse {
  results: {
    bindings: {
      genre: {
        type: "uri";
        value: string;
      };
      labelEn: {
        type: "literal";
        value: string;
        "xml:lang"?: "en";
      };
      labelRu: {
        type: "literal";
        value: string;
        "xml:lang"?: "ru";
      };
      wikipediaEnUrl?: {
        type: "uri";
        value: string;
      };
      wikipediaRuUrl?: {
        type: "uri";
        value: string;
      };
    }[];
  };
}

interface FetchPeopleResponse {
  results: {
    bindings: {
      person: {
        type: "uri";
        value: string;
      };
      fullNameEn: {
        type: "literal";
        value: string;
        "xml:lang"?: "en";
      };
      fullNameRu: {
        type: "literal";
        value: string;
        "xml:lang"?: "ru";
      };
      tmdbId?: {
        type: "literal";
        value: string;
      };
      wikipediaEnUrl?: {
        type: "uri";
        value: string;
      };
      wikipediaRuUrl?: {
        type: "uri";
        value: string;
      };
    }[];
  };
}
