import * as React from "react";

import { useAuthentication } from "./Authentication.js";
import Button from "./Button.js";

const SignUpBanner: React.FC = () => {
  const authentication = useAuthentication();
  const [kinopoiskUrl, setKinopoiskUrl] = React.useState("");
  const [name, setName] = React.useState("");
  const [email, setEmail] = React.useState("");
  const [sent, setSent] = React.useState(false);

  if (authentication.state !== "unauthenticated") {
    return null;
  }

  return (
    <aside className="container mx-auto mb-6 rounded-b-xl bg-zinc-100 px-5 py-8 text-lg antialiased">
      <div className="mx-auto mb-3 max-w-prose">
        <h1 className="text-4xl font-bold">
          Находит 💎 кино по твоим интересам
        </h1>
      </div>

      <p className="mx-auto mb-3 max-w-prose">
        Зырь использует твои оценки, чтобы находить людей, которые ставят
        похожие оценки. Затем он использует оценки этих людей, чтобы подыскивать
        тебе классные фильмы, которые ты не найдёшь никаким другим способом.
      </p>

      <p className="mx-auto mb-3 max-w-prose">
        Требуется аккаунт на КиноПоиске с как минимум 50 оценками.
      </p>

      <div className="mx-auto max-w-prose">
        <form
          onSubmit={(event) => {
            event.preventDefault();
            authentication.invite({
              email,
              kinopoiskUrl,
              name,
            });
            setSent(true);
          }}
        >
          <table>
            <tr>
              <td>
                <label htmlFor="kinopoisk">Профиль на КиноПоиске:</label>
              </td>
              <td>
                <input
                  id="kinopoisk"
                  type="url"
                  placeholder="https://www.kinopoisk.ru/user/789114/"
                  value={kinopoiskUrl}
                  onChange={(event) => setKinopoiskUrl(event.target.value)}
                  required
                  className="ml-4 block min-w-[360px] rounded border border-zinc-200 bg-white px-2 py-0.5 focus:ring-2 focus:ring-indigo-500"
                />
              </td>
            </tr>
            <tr>
              <td className="pt-1">
                <label htmlFor="name">Имя:</label>
              </td>
              <td className="pt-1">
                <input
                  id="name"
                  type="text"
                  placeholder="Константин Констанинопольский"
                  value={name}
                  onChange={(event) => setName(event.target.value)}
                  required
                  className="ml-4 block min-w-[310px] rounded border border-zinc-200 bg-white px-2 py-0.5 focus:ring-2 focus:ring-indigo-500"
                />
              </td>
            </tr>
            <tr>
              <td className="pt-1">
                <label htmlFor="email">Имейл для инвайта:</label>
              </td>
              <td className="pt-1">
                <input
                  id="email"
                  type="email"
                  placeholder="<EMAIL>"
                  value={email}
                  onChange={(event) => setEmail(event.target.value)}
                  required
                  className="ml-4 block rounded border border-zinc-200 bg-white px-2 py-0.5 focus:ring-2 focus:ring-indigo-500"
                />
              </td>
            </tr>
          </table>

          <div className="mt-5">
            {sent ? (
              <span className="text-green-600">
                Заявка принята. Ссылка на вход придёт по email в течение суток.
              </span>
            ) : (
              <Button variant="primary" size="md" type="submit">
                Запросить инвайт
              </Button>
            )}
          </div>
        </form>
      </div>
    </aside>
  );
};

export default SignUpBanner;
