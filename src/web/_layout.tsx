import * as React from "react";

import Header from "./components/Header.js";
import Search from "./components/Search.js";
import SignUpBanner from "./components/SignUpBanner.js";

// eslint-disable-next-line @typescript-eslint/ban-types
const Layout: React.FC<React.PropsWithChildren<{}>> = (props) => {
  return (
    <>
      <SignUpBanner />
      <Header />
      <div className="border-b border-zinc-200" />
      <Search />
      {props.children}
    </>
  );
};

export default Layout;
