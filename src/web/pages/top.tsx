import classNames from "classnames";
import * as React from "react";

import Head from "../components/Head.js";
import Join from "../components/Join.js";
import { useRouter } from "../components/Router.js";
import TabControl from "../components/TabControl.js";
import { graphql, useQuery } from "../graphql/client.js";

const GetTop = graphql(/* GraphQL */ `
  query GetTop($offset: Int) {
    movies(sort: BEST_FIRST, offset: $offset, limit: 250) {
      id
      kinopoiskId
      currentMark {
        mark
      }
      directors {
        fullName
      }
      slug
      title
      goodMarksPercentage
      bestMarksPercentage
      topPositionAllTime
      viewed
      year
    }
  }
`);

const TopPage: React.FC = () => {
  const router = useRouter();
  const offset = router.url.searchParams.get("offset")
    ? Number(router.url.searchParams.get("offset"))
    : null;
  const { data } = useQuery(GetTop, { offset });

  if (!data) {
    return null;
  }

  return (
    <main className="container mx-auto mb-10 px-13 text-lg antialiased">
      <Head>
        <title>Топ – Зырь</title>
      </Head>
      <h1 className="mt-2 mb-3 text-7xl font-bold tracking-tight">Топ</h1>

      <div className="mb-6">
        <TabControl
          items={[
            {
              label: "1..250",
              href: offset == null ? undefined : router.stringify("/top", {}),
              selected: offset == null,
            },
            {
              label: "251..500",
              href:
                offset === 250
                  ? undefined
                  : router.stringify("/top", {}) + "?offset=250",
              selected: offset === 250,
            },
            {
              label: "501..750",
              href:
                offset === 500
                  ? undefined
                  : router.stringify("/top", {}) + "?offset=500",
              selected: offset === 500,
            },
            {
              label: "750..1000",
              href:
                offset === 750
                  ? undefined
                  : router.stringify("/top", {}) + "?offset=750",
              selected: offset === 750,
            },
          ]}
        />
      </div>

      <ol className="relative space-y-2">
        {data.movies.map((movie) => (
          <li key={movie.id}>
            {movie.viewed ? (
              <span
                className={classNames(
                  "absolute left-[-31px] h-[23px] w-[23px] rounded-full text-center",
                  {
                    "text-[16px] tracking-tighter":
                      movie.currentMark && movie.currentMark.mark === 10,
                    "bg-pink-600 text-white":
                      movie.currentMark &&
                      movie.currentMark.mark >= 9 &&
                      movie.currentMark.mark <= 10,
                    "bg-indigo-600 text-white":
                      movie.currentMark &&
                      movie.currentMark.mark >= 7 &&
                      movie.currentMark.mark <= 8,
                    "bg-zinc-300 text-zinc-950":
                      movie.currentMark && movie.currentMark.mark <= 6,
                  },
                )}
              >
                {movie.currentMark?.mark ?? (
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 16 16"
                    className="mx-auto mt-[1px] h-[19px] w-[19px]"
                  >
                    <path
                      d="M8 12.5C3 12.5.3 8.4.2 8.3L0 8l.1-.3C.2 7.6 2.5 3.5 8 3.5s7.8 4.1 7.8 4.3l.2.3-.2.2c-.1.2-2.8 4.2-7.8 4.2zM1.2 8c.7.8 3.1 3.5 6.8 3.5 3.8 0 6.1-2.7 6.8-3.5-.6-.9-2.6-3.5-6.8-3.5-4.2 0-6.2 2.6-6.8 3.5z"
                      stroke="none"
                    />
                    <path
                      d="M8 10.5c-1.9 0-3.5-1.6-3.5-3.5S6.1 3.5 8 3.5s3.5 1.6 3.5 3.5-1.6 3.5-3.5 3.5zm0-6C6.6 4.5 5.5 5.6 5.5 7S6.6 9.5 8 9.5s2.5-1.1 2.5-2.5S9.4 4.5 8 4.5z"
                      stroke="none"
                    />
                    <circle cx="6.7" cy="6.5" r="1.5" />
                  </svg>
                )}
              </span>
            ) : null}
            {movie.topPositionAllTime}.{" "}
            <a
              className="text-indigo-800 transition-colors hover:text-indigo-500 hover:transition-none"
              href={
                movie.slug
                  ? router.stringify("/movies/:movieSlug", {
                      movieSlug: movie.slug,
                    })
                  : router.stringify("/movies/by-kinopoisk-id/:kinopoiskId", {
                      kinopoiskId: movie.kinopoiskId,
                    })
              }
            >
              {movie.title}
            </a>
            {" "}·{" "}
            <Join delimiter=", ">
              {movie.directors.map((d) => d.fullName).join(", ") || null}
              {movie.year}
            </Join>
            {" "}
            <span className="relative -top-0.5 inline-flex space-x-1.5 text-xs text-zinc-500">
              {movie.bestMarksPercentage ? (
                <span>
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="mr-px inline h-[0.8lh] w-[0.8lh]"
                  >
                    <path stroke="none" d="M0 0h24v24H0z" fill="none" />
                    <path d="M19.5 12.572l-7.5 7.428l-7.5 -7.428a5 5 0 1 1 7.5 -6.566a5 5 0 1 1 7.5 6.572" />
                  </svg>
                  {movie.bestMarksPercentage}%
                </span>
              ) : null}
              {movie.goodMarksPercentage != null ? (
                <span>
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="mr-px inline h-[0.8lh] w-[0.8lh]"
                  >
                    <path stroke="none" d="M0 0h24v24H0z" fill="none" />
                    <path d="M7 11v8a1 1 0 0 1 -1 1h-2a1 1 0 0 1 -1 -1v-7a1 1 0 0 1 1 -1h3a4 4 0 0 0 4 -4v-1a2 2 0 0 1 4 0v5h3a2 2 0 0 1 2 2l-1 5a2 3 0 0 1 -2 2h-7a3 3 0 0 1 -3 -3" />
                  </svg>
                  {movie.goodMarksPercentage}%
                </span>
              ) : null}
            </span>
          </li>
        ))}
      </ol>

      <div className="mt-6">
        <TabControl
          items={[
            {
              label: "1..250",
              href: offset == null ? undefined : router.stringify("/top", {}),
              selected: offset == null,
            },
            {
              label: "251..500",
              href:
                offset === 250
                  ? undefined
                  : router.stringify("/top", {}) + "?offset=250",
              selected: offset === 250,
            },
            {
              label: "501..750",
              href:
                offset === 500
                  ? undefined
                  : router.stringify("/top", {}) + "?offset=500",
              selected: offset === 500,
            },
            {
              label: "751..1000",
              href:
                offset === 750
                  ? undefined
                  : router.stringify("/top", {}) + "?offset=750",
              selected: offset === 750,
            },
          ]}
        />
      </div>
    </main>
  );
};

export default TopPage;
