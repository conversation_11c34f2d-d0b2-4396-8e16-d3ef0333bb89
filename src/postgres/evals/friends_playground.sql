WITH user_mark_mean_absolute_error AS (
  SELECT
    kinopoisk_movie_mark.user_id as user_id,
    another_kinopoisk_movie_mark.user_id as friend_id,
    SUM(
      ABS(
        (
          CASE
            WHEN kinopoisk_movie_mark.mark >= 9 THEN 1.0
            ELSE 0
          END
        )
        - (
          CASE
            WHEN another_kinopoisk_movie_mark.mark >= 9 THEN 1.0
            ELSE 0
          END
        ) + 0.0
      )
    ) / COUNT(*) as mae
  FROM
    account
  JOIN
    kinopoisk_movie_mark
    ON kinopoisk_movie_mark.user_id = account.kinopoisk_id
  JOIN
    kinopoisk_movie_mark another_kinopoisk_movie_mark
    ON another_kinopoisk_movie_mark.movie_id = kinopoisk_movie_mark.movie_id
    AND another_kinopoisk_movie_mark.user_id != kinopoisk_movie_mark.user_id
  WHERE
    kinopoisk_movie_mark.mark >= 9
    OR another_kinopoisk_movie_mark.mark >= 9
  GROUP BY
    kinopoisk_movie_mark.user_id,
    another_kinopoisk_movie_mark.user_id
  HAVING
    COUNT(*) >= 50
), ordered_friend AS (
  SELECT
    user_mark_mean_absolute_error.user_id as user_id,
    user_mark_mean_absolute_error.friend_id,
    ROW_NUMBER() OVER (
      PARTITION BY
        user_mark_mean_absolute_error.user_id
      ORDER BY
        user_mark_mean_absolute_error.mae ASC NULLS LAST,
        user_mark_mean_absolute_error.friend_id ASC
    ) as "order"
  FROM
    user_mark_mean_absolute_error
), graphql_friend AS (
  SELECT
    ordered_friend.user_id as user_id,
    ordered_friend.friend_id as friend_id,
    ordered_friend."order" as "order"
  FROM
    ordered_friend
  WHERE
    ordered_friend."order" <= 200
)

SELECT
  graphql_friend."order",
  kinopoisk_user.name
FROM
  graphql_friend
JOIN
  kinopoisk_user
  ON kinopoisk_user.id = graphql_friend.friend_id
WHERE
  graphql_friend.user_id = 789114
  AND EXISTS (SELECT FROM kinopoisk_friend WHERE kinopoisk_friend.user_id = graphql_friend.user_id AND kinopoisk_friend.friend_id = graphql_friend.friend_id)
ORDER BY
  graphql_friend."order" ASC
